package com.bilibili.miniapp.open.common.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * <AUTHOR>
 * @date 2025/6/4
 */
@AllArgsConstructor
@Getter
public enum WithdrawStatus {

    //0-提现冻结，1-可提现，2-提现中，3-提现成功
    FROZEN(0, "提现冻结"),
    READY_TO_WITHDRAW(1, "可提现"),
    WITHDRAWING(2, "提现中"),
    SUCCESS(3, "提现成功"),
    ;

    private final int code;
    private final String desc;

}
