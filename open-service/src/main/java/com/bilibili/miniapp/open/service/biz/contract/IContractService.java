package com.bilibili.miniapp.open.service.biz.contract;

import com.bilibili.miniapp.open.common.enums.ContractStatusEnum;
import com.bilibili.miniapp.open.service.bo.contract.ContractSettlementDetailBo;
import com.bilibili.miniapp.open.service.bo.contract.ContractSignUrlBo;
import com.bilibili.miniapp.open.service.bo.contract.SettlementContractBo;

import java.time.LocalDate;

/**
 * 合同结算服务接口
 *
 * <AUTHOR>
 * @date 2025/1/15
 */
public interface IContractService {

    void saveContractSettlement(Long mid, SettlementContractBo request);

    ContractSettlementDetailBo getContractDetail(Long mid, String appId);

    ContractSignUrlBo getContractSignUrl(Long mid, String appId);

    void updateContractStatusFromDataBus(String contractId, ContractStatusEnum status, long updateTime);

    void checkContractTimeJob(LocalDate date);
}
