package com.bilibili.miniapp.open.service.biz.contract.impl;

import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSON;
import com.bilibili.mall.miniapp.dto.miniapp.channel.ChannelMiniAppInfoDTO;
import com.bilibili.miniapp.open.common.enums.ContractStatusEnum;
import com.bilibili.miniapp.open.common.enums.ErrorCodeType;
import com.bilibili.miniapp.open.common.enums.MiniAppPermission;
import com.bilibili.miniapp.open.common.exception.ServiceException;
import com.bilibili.miniapp.open.common.util.AssertUtil;
import com.bilibili.miniapp.open.repository.mysql.mallapp.dao.MiniAppOpenContractDao;
import com.bilibili.miniapp.open.repository.mysql.mallapp.po.MiniAppOpenContractPo;
import com.bilibili.miniapp.open.repository.mysql.mallapp.po.MiniAppOpenContractPoExample;
import com.bilibili.miniapp.open.service.aspect.LockRequest;
import com.bilibili.miniapp.open.service.biz.account.IAccountService;
import com.bilibili.miniapp.open.service.biz.company.ICompanyService;
import com.bilibili.miniapp.open.service.biz.contract.IContractService;
import com.bilibili.miniapp.open.service.bo.company.CompanyDetailBo;
import com.bilibili.miniapp.open.service.bo.contract.*;
import com.bilibili.miniapp.open.service.config.ConfigCenter;
import com.bilibili.miniapp.open.service.enums.ContractCenterContractType;
import com.bilibili.miniapp.open.service.enums.ContractCenterEntityType;
import com.bilibili.miniapp.open.service.rpc.http.dto.*;
import com.bilibili.miniapp.open.service.rpc.http.impl.ContractCenterApiService;
import com.bilibili.miniapp.open.service.rpc.http.impl.MiniAppRemoteService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import java.lang.reflect.InvocationTargetException;
import java.lang.reflect.Method;
import java.sql.Timestamp;
import java.time.LocalDate;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 合同结算服务实现
 *
 * <AUTHOR>
 * @date 2025/1/15
 */
@Slf4j
@Service
public class ContractService implements IContractService {

    @Autowired
    private IAccountService accountService;

    @Autowired
    private ContractCenterApiService contractCenterApiService;

    @Autowired
    private MiniAppOpenContractDao miniAppOpenContractDao;
    @Autowired
    private ICompanyService companyService;
    @Autowired
    private ConfigCenter configCenter;
    @Autowired
    private MiniAppRemoteService miniAppRemoteService;

    @Override
    @Transactional(rollbackFor = Exception.class)
    @LockRequest(key = "'create_settlement_contract_' + #request.appId")
    public void saveContractSettlement(Long mid, SettlementContractBo request) {

        CompanyDetailBo company = companyService.getCreatedCompanyDetail(mid);

        AssertUtil.isTrue(accountService.hasPermission(mid, request.getAppId(), MiniAppPermission.SUPER_ADMIN), ErrorCodeType.NO_PERMISSION);

        MiniAppOpenContractPo existedContact = getContract(request.getAppId());
        boolean allowCreation = existedContact == null
                || List.of(ContractStatusEnum.EXPIRED.getCode(), ContractStatusEnum.AUDIT_FAILED.getCode()).contains(existedContact.getContractStatus());
        AssertUtil.isTrue(allowCreation, ErrorCodeType.EXISTS_DATA.getCode(), "合同已存在，请勿重复创建");

        validateContractTime(request);

        CreateContractResult contractResult = createContract(request, company);

        deleteAuditFailContractIfNecessary(existedContact);

        saveContract(request, contractResult.getContractId());
    }

    private void deleteAuditFailContractIfNecessary(MiniAppOpenContractPo existedContact) {
        if (existedContact == null) {
            return;
        }

        MiniAppOpenContractPo delete = new MiniAppOpenContractPo();
        delete.setId(existedContact.getId());
        delete.setIsDeleted(1);

        miniAppOpenContractDao.updateByPrimaryKeySelective(delete);
    }


    private CreateContractResult createContract(SettlementContractBo request, CompanyDetailBo company) {
        CreateContractRequest contractRequest = buildCreateContractRequest(company);
        CreateContractResult createContractResult = contractCenterApiService.createContract(contractRequest);
        String contractId = createContractResult.getContractId();

        LocalDate contractStartTime = new Timestamp(request.getContractStartTime()).toLocalDateTime().toLocalDate();
        LocalDate contractEndTime = new Timestamp(request.getContractEndTime()).toLocalDateTime().toLocalDate();
        ChannelMiniAppInfoDTO miniApp = miniAppRemoteService.queryAppInfoWithinCache(request.getAppId());
        FillContractBo fillBo = FillContractBo.builder()
                .appId(request.getAppId())
                .signatoryName(request.getSignatoryName())
                .signatoryPhone(request.getSignatoryPhone())
                .signatoryEmail(request.getSignatoryEmail())
                .contactAddress(request.getContactAddress())
                .startYear(String.valueOf(contractStartTime.getYear()))
                .startMonth(String.valueOf(contractStartTime.getMonthValue()))
                .startDay(String.valueOf(contractStartTime.getDayOfMonth()))
                .endYear(String.valueOf(contractEndTime.getYear()))
                .endMonth(String.valueOf(contractEndTime.getMonthValue()))
                .endDay(String.valueOf(contractEndTime.getDayOfMonth()))
                //.companyName(company.getCompanyInfo().getCompanyName())
                //todo 航宇，测试使用，上线后需要修改回注释的内容
                .companyName("上海宽娱数码科技有限公司")
                .appName(miniApp.getName())
                .build();
        contractCenterApiService.fillContract(buildFillContractDto(contractId, fillBo));

        return createContractResult;
    }

    private FillContractDto buildFillContractDto(String contractId, FillContractBo fillContractBo) {
        Map<String, String> contractFormMap = configCenter.getContractConfig().getContractFormMap();
        Map<String, String> formInfos = new HashMap<>();
        List<ContractFormSingleLineBo> params = new ArrayList<>();
        Class<?> boClass = fillContractBo.getClass();

        for (Map.Entry<String, String> entry : contractFormMap.entrySet()) {
            //待填写位置名称
            String formKey = entry.getKey();
            String boFieldName = entry.getValue();
            try {
                String methodName = "get" + boFieldName.substring(0, 1).toUpperCase() + boFieldName.substring(1);
                Method method = boClass.getMethod(methodName);
                Object value = method.invoke(fillContractBo);

                if (value != null) {
                    params.add(new ContractFormSingleLineBo(formKey, value.toString()));
                }
            } catch (NoSuchMethodException | IllegalAccessException | InvocationTargetException e) {
                throw new ServiceException(ErrorCodeType.BAD_REQUEST.getCode(), StrUtil.format("填写合同时获取字段失败: {}, 错误: {}", boFieldName, e.getMessage()));
            }
        }
        formInfos.put(configCenter.getContractConfig().getContractTemplateId(), JSON.toJSONString(params));

        FillContractDto fillContractDto = new FillContractDto();
        fillContractDto.setContractId(contractId);
        fillContractDto.setFormInfos(formInfos);

        return fillContractDto;
    }

    /**
     * 校验合同时间参数
     */
    private void validateContractTime(SettlementContractBo request) {
        Long startTime = request.getContractStartTime();
        Long endTime = request.getContractEndTime();

        AssertUtil.isTrue(startTime > 0 && endTime > 0,
                ErrorCodeType.BAD_PARAMETER.getCode(), "合同时间参数不正确");

        AssertUtil.isTrue(endTime > startTime,
                ErrorCodeType.BAD_PARAMETER.getCode(), "合同结束时间必须大于开始时间");
    }

    /**
     * 构建合同创建请求
     */
    private CreateContractRequest buildCreateContractRequest(CompanyDetailBo company) {

        //todo 航宇，测试使用，上线后需要修改回注释的内容
        ContractEntityDto contractEntity = ContractEntityDto.builder()
                .entityPartner("乙方")
                .entityType(ContractCenterEntityType.COMPANY.getCode())
                //.entityName(company.getCompanyInfo().getCompanyName())
                .entityName("上海宽娱数码科技有限公司")
                .entityIdentity("91310110779301025N")
                //.entityIdentity(company.getCompanyInfo().getCreditCode())
                .build();

        ContractEntityDto bilibiliInfo = ContractEntityDto.builder()
                .entityPartner("甲方")
                .entityType(ContractCenterEntityType.OUR_COMPANY.getCode())
                .entityName(configCenter.getSettlementConfig().getCompanyName())
                .build();

        String oaTestJson = "{\n" +
                "    \"acceptance\": \"验收\",\n" +
                "    \"amount\": 100,\n" +
                "    \"business_risk\": \"风险\",\n" +
                "    \"contract_full_name\": \"小程序测试\",\n" +
                "    \"contract_id\": \"\",\n" +
                "    \"contract_type\": \"企业投资TZ\",\n" +
                "    \"cooperation\": \"模式\",\n" +
                "    \"currency\": \"人民币\",\n" +
                "    \"end_time\": null,\n" +
                "    \"extension\": \"续约\",\n" +
                "    \"financial\": \"条款\",\n" +
                "    \"id\": null,\n" +
                "    \"is_framework_contract\": 0,\n" +
                "    \"over_budget\": 2,\n" +
                "    \"payment\": \"收款\",\n" +
                "    \"payment_type\": \"固定金额\",\n" +
                "    \"property\": \"产权\",\n" +
                "    \"start_time\": null,\n" +
                "    \"title\": null\n" +
                "  }";
        return CreateContractRequest.builder()
                .business("小程序开放平台")
                .entity(List.of("机构"))
                .contractType(ContractCenterContractType.COMPANY.getCode())
                .entities(List.of(bilibiliInfo, contractEntity))
                //.oaInfo(new OaInfo())
                .oaInfo(JSON.parseObject(oaTestJson, OaInfo.class))
                .requiredTplId(configCenter.getContractConfig().getContractTemplateId())
                .operator(configCenter.getContractConfig().getCreateContractOperator())
                .build();
    }

    private void saveContract(SettlementContractBo request, String contractId) {

        MiniAppOpenContractPo contractPo = MiniAppOpenContractPo.builder()
                .appId(request.getAppId())
                .signatoryName(request.getSignatoryName())
                .signatoryPhone(request.getSignatoryPhone())
                .signatoryEmail(request.getSignatoryEmail())
                .contactAddress(request.getContactAddress())
                .contractStartTime(new Timestamp(request.getContractStartTime()))
                .contractEndTime(new Timestamp(request.getContractEndTime()))
                .contractId(contractId)
                .contractStatus(ContractStatusEnum.PENDING_AUDIT.getCode())
                .contractMtime(new Timestamp(System.currentTimeMillis()))
                .build();

        miniAppOpenContractDao.insertSelective(contractPo);
    }


    @Override
    public ContractSettlementDetailBo getContractDetail(Long mid, String appId) {
        AssertUtil.isTrue(accountService.hasPermission(mid, appId, MiniAppPermission.SUPER_ADMIN), ErrorCodeType.NO_PERMISSION);

        MiniAppOpenContractPo contract = getContract(appId);
        if (contract == null) {
            return null;
        }

        ContractDetailDto contractDetail = contractCenterApiService.getContractDetail(contract.getContractId());

        return ContractSettlementDetailBo.builder()
                .contractId(contract.getContractId())
                .signatoryName(contract.getSignatoryName())
                .signatoryPhone(contract.getSignatoryPhone())
                .signatoryEmail(contract.getSignatoryEmail())
                .contactAddress(contract.getContactAddress())
                .contractStartTime(contract.getContractStartTime().getTime())
                .contractEndTime(contract.getContractEndTime().getTime())
                .contractStatus(contract.getContractStatus())
                .downloadUrl(contractDetail.getDownloadUrl())
                .build();
    }

    private MiniAppOpenContractPo getContract(String appId) {
        MiniAppOpenContractPoExample example = new MiniAppOpenContractPoExample();
        example.createCriteria()
                .andAppIdEqualTo(appId)
                .andIsDeletedEqualTo(0);

        List<MiniAppOpenContractPo> contracts = miniAppOpenContractDao.selectByExample(example);
        if (CollectionUtils.isEmpty(contracts)) {
            return null;
        }
        return contracts.get(0);
    }

    @Override
    public ContractSignUrlBo getContractSignUrl(Long mid, String appId) {

        AssertUtil.isTrue(accountService.hasPermission(mid, appId, MiniAppPermission.SUPER_ADMIN), ErrorCodeType.NO_PERMISSION);

        MiniAppOpenContractPo contract = getContract(appId);
        if (contract == null) {
            return null;
        }

        ContractSignUrlResult signUrlResult = contractCenterApiService.getContractSignUrl(contract.getContractId());
        return ContractSignUrlBo.builder()
                .signUrl(signUrlResult == null ? null : signUrlResult.getSignUrl())
                .build();
    }

    @Override
    public void updateContractStatus(String contractId, ContractStatusEnum status, long updateTime) {

        MiniAppOpenContractPoExample example = new MiniAppOpenContractPoExample();
        example.createCriteria()
                .andContractIdEqualTo(contractId)
                .andIsDeletedEqualTo(0);

        List<MiniAppOpenContractPo> contracts = miniAppOpenContractDao.selectByExample(example);
        if (CollectionUtils.isEmpty(contracts)) {
            return;
        }

        MiniAppOpenContractPo existedPo = contracts.get(0);

        if (status == ContractStatusEnum.INACTIVE) {
            LocalDate startDay = existedPo.getContractStartTime().toLocalDateTime().toLocalDate();
            if (!startDay.isBefore(LocalDate.now())) {
                status = ContractStatusEnum.EFFECTIVE;
            }
        }


        Timestamp updateTimeStamp = new Timestamp(updateTime * 1000);
        MiniAppOpenContractPo updatePo = new MiniAppOpenContractPo();
        updatePo.setContractStatus(status.getCode());
        updatePo.setContractMtime(updateTimeStamp);

        MiniAppOpenContractPoExample updateExample = new MiniAppOpenContractPoExample();
        updateExample.createCriteria()
                .andAppIdEqualTo(existedPo.getAppId())
                .andContractIdEqualTo(contractId)
                .andContractStatusEqualTo(existedPo.getContractStatus())
                .andContractMtimeLessThan(updateTimeStamp)
                .andIsDeletedEqualTo(0);

        miniAppOpenContractDao.updateByExampleSelective(updatePo, updateExample);
    }

    @Override
    public void checkContractTimeJob(LocalDate date) {
        if (date == null) {
            date = LocalDate.now();
        }
        processEffect(date);
        processExpired(date);
    }

    private void processEffect(LocalDate date) {
        MiniAppOpenContractPoExample example = new MiniAppOpenContractPoExample();
        example.createCriteria()
                .andContractStartTimeGreaterThanOrEqualTo(Timestamp.valueOf(date.atStartOfDay()))
                .andIsDeletedEqualTo(0);

        List<MiniAppOpenContractPo> effectiveContracts = miniAppOpenContractDao.selectByExample(example);

        if (CollectionUtils.isEmpty(effectiveContracts)) {
            return;
        }
        List<String> contractIds = effectiveContracts.stream()
                .map(MiniAppOpenContractPo::getContractId)
                .collect(Collectors.toList());
        doUpdateContractStatus(contractIds, ContractStatusEnum.EXPIRED);
    }

    private void processExpired(LocalDate date) {
        MiniAppOpenContractPoExample example = new MiniAppOpenContractPoExample();
        example.createCriteria()
                .andContractEndTimeLessThan(Timestamp.valueOf(date.atStartOfDay()))
                .andIsDeletedEqualTo(0);

        List<MiniAppOpenContractPo> expiredContracts = miniAppOpenContractDao.selectByExample(example);

        if (CollectionUtils.isEmpty(expiredContracts)) {
            return;
        }
        List<String> contractIds = expiredContracts.stream()
                .map(MiniAppOpenContractPo::getContractId)
                .collect(Collectors.toList());
        doUpdateContractStatus(contractIds, ContractStatusEnum.EXPIRED);
    }

    private void doUpdateContractStatus(List<String> contractIds, ContractStatusEnum targetStatus) {
        MiniAppOpenContractPo updatePo = new MiniAppOpenContractPo();
        updatePo.setContractStatus(targetStatus.getCode());
        updatePo.setContractMtime(new Timestamp(System.currentTimeMillis()));

        MiniAppOpenContractPoExample updateExample = new MiniAppOpenContractPoExample();
        updateExample.createCriteria()
                .andContractIdIn(contractIds);
        miniAppOpenContractDao.updateByExampleSelective(updatePo, updateExample);
    }
}
