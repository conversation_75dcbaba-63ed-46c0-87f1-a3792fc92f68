package com.bilibili.miniapp.open.service.config;

import lombok.Data;

import java.util.Map;

/**
 * <AUTHOR>
 * @date 2025/5/30
 */
@Data
public class ContractConfig {

    /**
     * 模板合同id，需要在合同中心创建，联系产品
     */
    private String contractTemplateId = "TPL569890775559604334";

    private Map<String ,String> contractFormMap = Map.of(
            "乙方-基本信息-联系地址", "contactAddress",
            "乙方-基本信息-联系人", "signatoryPhone",
            "乙方-基本信息-手机", "signatoryPhone",
            "乙方-基本信息-电子邮件", "signatoryEmail",
            "结束年", "endYear",
            "结束月", "endMonth",
            "结束日", "endDay",
            "乙方-基本信息-企业名称", "contactAddress",
            "乙方-基本信息-小程序APPID", "appId",
            "乙方-基本信息-小程序名称", "appName"
    );

    private String createContractOperator = "zhangkaige01";
}
