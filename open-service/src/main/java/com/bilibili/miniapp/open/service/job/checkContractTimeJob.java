package com.bilibili.miniapp.open.service.job;

import cn.hutool.core.date.LocalDateTimeUtil;
import com.bilibili.miniapp.open.service.biz.contract.IContractService;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.annotation.JobHandler;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.time.LocalDate;

/**
 * <AUTHOR>
 * @date 2025/6/10
 */
@Component
@JobHandler("checkContractTimeJob")
@Slf4j
public class checkContractTimeJob extends AbstractJobHandler {

    @Autowired
    private IContractService contractService;

    @Override
    public ReturnT<String> doExecute(String param) throws Exception {
        LocalDate date = StringUtils.isBlank(param) ? null : LocalDateTimeUtil.parse(param, "yyyy-MM-dd").toLocalDate();
        contractService.checkContractTimeJob(date);
        return ReturnT.SUCCESS;
    }
}
